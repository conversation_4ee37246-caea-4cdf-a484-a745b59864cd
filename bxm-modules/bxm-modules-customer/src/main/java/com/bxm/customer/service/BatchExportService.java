package com.bxm.customer.service;

import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.customer.domain.dto.valueadded.BatchErrorDataDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationExportDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批量导出服务
 *
 * 提供批量操作结果和错误数据的Excel导出功能
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
public class BatchExportService {

    @Autowired
    private IBatchValueAddedDeliveryOrderService batchValueAddedDeliveryOrderService;

    /**
     * 导出批量操作错误数据到Excel
     *
     * @param errorDataBatchNo 错误数据批次号
     * @param response HTTP响应对象
     * @throws IOException 当导出失败时抛出
     */
    public void exportBatchErrorData(String errorDataBatchNo, HttpServletResponse response) throws IOException {
        log.info("Starting export batch error data for errorDataBatchNo: {}", errorDataBatchNo);

        try {
            // 获取错误数据列表
            List<BatchErrorDataDTO> errorDataList = batchValueAddedDeliveryOrderService.getBatchErrorDataList(errorDataBatchNo);
            
            if (errorDataList == null || errorDataList.isEmpty()) {
                log.warn("No error data found for errorDataBatchNo: {}", errorDataBatchNo);
                throw new RuntimeException("未找到错误数据");
            }

            // 生成文件名
            String fileName = generateErrorDataFileName(errorDataBatchNo);

            // 使用ExcelUtil导出
            ExcelUtil<BatchErrorDataDTO> util = new ExcelUtil<>(BatchErrorDataDTO.class);
            util.exportExcel(response, errorDataList, "批量操作错误数据", fileName);

            log.info("Successfully exported {} error records for errorDataBatchNo: {}", 
                    errorDataList.size(), errorDataBatchNo);

        } catch (Exception e) {
            log.error("Failed to export batch error data for errorDataBatchNo: {}", errorDataBatchNo, e);
            throw new IOException("导出错误数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出批量操作详细记录到Excel
     *
     * @param batchNo 批次号
     * @param response HTTP响应对象
     * @throws IOException 当导出失败时抛出
     */
    public void exportBatchOperationDetails(String batchNo, HttpServletResponse response) throws IOException {
        log.info("Starting export batch operation details for batchNo: {}", batchNo);

        try {
            // 获取批量操作结果
            BatchOperationResult operationResult = batchValueAddedDeliveryOrderService.getBatchOperationResult(batchNo);
            if (operationResult == null) {
                log.warn("No operation result found for batchNo: {}", batchNo);
                throw new RuntimeException("未找到批量操作结果");
            }

            // 构建导出数据
            List<BatchOperationExportDTO> exportDataList = buildExportDataList(operationResult);

            // 如果有错误数据，也包含进来
            if (operationResult.getHasErrors() && operationResult.getErrorDataBatchNo() != null) {
                List<BatchErrorDataDTO> errorDataList = batchValueAddedDeliveryOrderService
                        .getBatchErrorDataList(operationResult.getErrorDataBatchNo());
                if (errorDataList != null && !errorDataList.isEmpty()) {
                    List<BatchOperationExportDTO> errorExportData = convertErrorDataToExportData(errorDataList, operationResult);
                    exportDataList.addAll(errorExportData);
                }
            }

            // 生成文件名
            String fileName = generateOperationDetailsFileName(batchNo, operationResult.getOperationType());

            // 使用ExcelUtil导出
            ExcelUtil<BatchOperationExportDTO> util = new ExcelUtil<>(BatchOperationExportDTO.class);
            util.exportExcel(response, exportDataList, "批量操作详细记录", fileName);

            log.info("Successfully exported {} operation records for batchNo: {}", 
                    exportDataList.size(), batchNo);

        } catch (Exception e) {
            log.error("Failed to export batch operation details for batchNo: {}", batchNo, e);
            throw new IOException("导出批量操作详细记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过唯一码导出错误数据
     *
     * @param uniqueCode 唯一导出码（实际上就是errorDataBatchNo）
     * @param response HTTP响应对象
     * @throws IOException 当导出失败时抛出
     */
    public void exportByUniqueCode(String uniqueCode, HttpServletResponse response) throws IOException {
        log.info("Starting export by unique code: {}", uniqueCode);

        // 唯一码就是errorDataBatchNo，直接调用错误数据导出方法
        exportBatchErrorData(uniqueCode, response);
    }

    /**
     * 构建导出数据列表
     */
    private List<BatchOperationExportDTO> buildExportDataList(BatchOperationResult operationResult) {
        List<BatchOperationExportDTO> exportDataList = new ArrayList<>();

        // 添加成功记录
        if (operationResult.getSuccessDeliveryOrderNos() != null) {
            for (String deliveryOrderNo : operationResult.getSuccessDeliveryOrderNos()) {
                BatchOperationExportDTO exportDTO = BatchOperationExportDTO.builder()
                        .batchNo(operationResult.getBatchNo())
                        .operationType(operationResult.getOperationType())
                        .operationTypeDesc(getOperationTypeDescription(operationResult.getOperationType()))
                        .deliveryOrderNo(deliveryOrderNo)
                        .result("SUCCESS")
                        .operatorName(operationResult.getOperatorName())
                        .operationTime(operationResult.getEndTime())
                        .build();
                exportDataList.add(exportDTO);
            }
        }

        return exportDataList;
    }

    /**
     * 将错误数据转换为导出数据
     */
    private List<BatchOperationExportDTO> convertErrorDataToExportData(List<BatchErrorDataDTO> errorDataList, 
                                                                       BatchOperationResult operationResult) {
        return errorDataList.stream()
                .map(errorData -> BatchOperationExportDTO.builder()
                        .batchNo(operationResult.getBatchNo())
                        .operationType(operationResult.getOperationType())
                        .operationTypeDesc(errorData.getOperationTypeDesc())
                        .deliveryOrderNo(errorData.getDeliveryOrderNo())
                        .creditCode(errorData.getCreditCode())
                        .customerName(errorData.getCustomerName())
                        .title(errorData.getTitle())
                        .topDeptName(errorData.getTopDeptName())
                        .originalStatus(errorData.getCurrentStatus())
                        .originalStatusDesc(errorData.getCurrentStatusDesc())
                        .targetStatus(errorData.getTargetStatus())
                        .targetStatusDesc(errorData.getTargetStatusDesc())
                        .result("FAILED")
                        .errorMsg(errorData.getErrorMsg())
                        .operatorName(errorData.getOperatorName())
                        .operationTime(operationResult.getEndTime())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 生成错误数据文件名
     */
    private String generateErrorDataFileName(String errorDataBatchNo) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("批量操作错误数据_%s_%s.xlsx", errorDataBatchNo, timestamp);
    }

    /**
     * 生成操作详细记录文件名
     */
    private String generateOperationDetailsFileName(String batchNo, String operationType) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String operationDesc = getOperationTypeDescription(operationType);
        return String.format("%s详细记录_%s_%s.xlsx", operationDesc, batchNo, timestamp);
    }

    /**
     * 获取操作类型描述
     */
    private String getOperationTypeDescription(String operationType) {
        switch (operationType) {
            case "BATCH_CONFIRM":
                return "批量确认";
            case "BATCH_SUBMIT":
                return "批量提交";
            case "BATCH_CLOSE_DEDUCTION":
                return "批量关闭扣款";
            case "BATCH_CLOSE_DELIVERY":
                return "批量关闭交付";
            case "BATCH_RESOLVE_DEDUCTION_EXCEPTION":
                return "批量解除扣款异常";
            case "BATCH_RESOLVE_DELIVERY_EXCEPTION":
                return "批量解除交付异常";
            case "BATCH_REJECT_FROM_CLOSED_DELIVERY":
                return "批量驳回";
            case "BATCH_RETURN_FROM_CLOSED_DEDUCTION":
                return "批量退回";
            default:
                return "批量操作";
        }
    }
}
