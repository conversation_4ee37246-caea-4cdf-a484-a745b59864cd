package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.customer.domain.dto.valueadded.BatchErrorDataDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationResult;
import com.bxm.customer.domain.dto.valueadded.BatchStatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationProgress;
import com.bxm.customer.service.BatchExportService;
import com.bxm.customer.service.IBatchValueAddedDeliveryOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 批量增值交付单控制器
 *
 * 提供增值交付单的批量操作接口
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Api(tags = "批量增值交付单管理")
@RestController
@RequestMapping("/customer/batch-value-added-delivery-order")
@Slf4j
public class BatchValueAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IBatchValueAddedDeliveryOrderService batchValueAddedDeliveryOrderService;

    @Autowired
    private BatchExportService batchExportService;

    /**
     * 批量确认交付单
     */
    @PostMapping("/batchConfirm")
    @ApiOperation(value = "批量确认交付单", notes = "提交人批量确认交付单，从已提交待交付状态转换到已交付待确认状态")
    @Log(title = "Batch confirm delivery orders", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchConfirm(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch confirm request for {} orders", request.getDeliveryOrderNos().size());
            
            // 设置操作类型
            request.setBatchOperationType("BATCH_CONFIRM");
            
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch confirm completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch confirm validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch confirm failed", e);
            return Result.fail("批量确认失败");
        }
    }

    /**
     * 批量提交交付单
     */
    @PostMapping("/batchSubmit")
    @ApiOperation(value = "批量提交交付单", notes = "提交人批量提交交付单，从已保存待提交状态转换到已提交待交付状态")
    @Log(title = "Batch submit delivery orders", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchSubmit(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch submit request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_SUBMIT");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch submit completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch submit validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch submit failed", e);
            return Result.fail("批量提交失败");
        }
    }

    /**
     * 批量关闭扣款
     */
    @PostMapping("/batchCloseDeduction")
    @ApiOperation(value = "批量关闭扣款", notes = "提交人批量关闭扣款，从已确认待扣款状态转换到已关闭扣款状态")
    @Log(title = "Batch close deduction", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchCloseDeduction(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch close deduction request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_CLOSE_DEDUCTION");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch close deduction completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch close deduction validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch close deduction failed", e);
            return Result.fail("批量关闭扣款失败");
        }
    }

    /**
     * 批量关闭交付
     */
    @PostMapping("/batchCloseDelivery")
    @ApiOperation(value = "批量关闭交付", notes = "提交人批量关闭交付，从已提交待交付状态转换到已关闭交付状态")
    @Log(title = "Batch close delivery", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchCloseDelivery(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch close delivery request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_CLOSE_DELIVERY");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch close delivery completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch close delivery validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch close delivery failed", e);
            return Result.fail("批量关闭交付失败");
        }
    }

    /**
     * 批量解除扣款异常
     */
    @PostMapping("/batchResolveDeductionException")
    @ApiOperation(value = "批量解除扣款异常", notes = "提交人批量解除扣款异常，从扣款异常状态转换到已确认待扣款状态")
    @Log(title = "Batch resolve deduction exception", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchResolveDeductionException(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch resolve deduction exception request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_RESOLVE_DEDUCTION_EXCEPTION");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch resolve deduction exception completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch resolve deduction exception validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch resolve deduction exception failed", e);
            return Result.fail("批量解除扣款异常失败");
        }
    }

    /**
     * 批量解除交付异常
     */
    @PostMapping("/batchResolveDeliveryException")
    @ApiOperation(value = "批量解除交付异常", notes = "提交人批量解除交付异常，从交付异常状态转换到已交付待确认状态")
    @Log(title = "Batch resolve delivery exception", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchResolveDeliveryException(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch resolve delivery exception request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_RESOLVE_DELIVERY_EXCEPTION");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch resolve delivery exception completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch resolve delivery exception validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch resolve delivery exception failed", e);
            return Result.fail("批量解除交付异常失败");
        }
    }

    /**
     * 批量驳回（从已关闭交付状态）
     */
    @PostMapping("/batchRejectFromClosedDelivery")
    @ApiOperation(value = "批量驳回", notes = "前置状态为已关闭交付的状态进行批量驳回操作")
    @Log(title = "Batch reject from closed delivery", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchRejectFromClosedDelivery(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch reject from closed delivery request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_REJECT_FROM_CLOSED_DELIVERY");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch reject from closed delivery completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch reject from closed delivery validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch reject from closed delivery failed", e);
            return Result.fail("批量驳回失败");
        }
    }

    /**
     * 批量退回（从已关闭扣款状态）
     */
    @PostMapping("/batchReturnFromClosedDeduction")
    @ApiOperation(value = "批量退回", notes = "前置状态为已关闭扣款状态进行批量退回操作")
    @Log(title = "Batch return from closed deduction", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResult> batchReturnFromClosedDeduction(@Valid @RequestBody BatchStatusChangeRequestDTO request) {
        try {
            log.info("Batch return from closed deduction request for {} orders", request.getDeliveryOrderNos().size());
            
            request.setBatchOperationType("BATCH_RETURN_FROM_CLOSED_DEDUCTION");
            BatchOperationResult result = batchValueAddedDeliveryOrderService.batchChangeStatus(request);
            
            log.info("Batch return from closed deduction completed, batchNo: {}, success: {}, fail: {}", 
                    result.getBatchNo(), result.getSuccessCount(), result.getFailCount());
            
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch return from closed deduction validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch return from closed deduction failed", e);
            return Result.fail("批量退回失败");
        }
    }

    /**
     * 获取批量操作结果
     */
    @GetMapping("/getBatchOperationResult/{batchNo}")
    @ApiOperation(value = "获取批量操作结果", notes = "根据批次号查询批量操作的结果信息")
    public Result<BatchOperationResult> getBatchOperationResult(@PathVariable @ApiParam("批次号") String batchNo) {
        try {
            BatchOperationResult result = batchValueAddedDeliveryOrderService.getBatchOperationResult(batchNo);
            if (result == null) {
                return Result.fail("未找到批次号对应的操作结果");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("Get batch operation result failed for batchNo: {}", batchNo, e);
            return Result.fail("获取批量操作结果失败");
        }
    }

    /**
     * 获取批量操作进度
     */
    @GetMapping("/getBatchOperationProgress/{batchNo}")
    @ApiOperation(value = "获取批量操作进度", notes = "根据批次号查询批量操作的实时进度信息")
    public Result<BatchOperationProgress> getBatchOperationProgress(@PathVariable @ApiParam("批次号") String batchNo) {
        try {
            BatchOperationProgress progress = batchValueAddedDeliveryOrderService.getBatchOperationProgress(batchNo);
            if (progress == null) {
                return Result.fail("未找到批次号对应的进度信息");
            }
            return Result.ok(progress);
        } catch (Exception e) {
            log.error("Get batch operation progress failed for batchNo: {}", batchNo, e);
            return Result.fail("获取批量操作进度失败");
        }
    }

    /**
     * 获取批量操作错误数据
     */
    @GetMapping("/getBatchErrorData/{errorDataBatchNo}")
    @ApiOperation(value = "获取批量操作错误数据", notes = "根据错误数据批次号查询失败记录的详细信息")
    public Result<List<BatchErrorDataDTO>> getBatchErrorData(@PathVariable @ApiParam("错误数据批次号") String errorDataBatchNo) {
        try {
            List<BatchErrorDataDTO> errorDataList = batchValueAddedDeliveryOrderService.getBatchErrorDataList(errorDataBatchNo);
            return Result.ok(errorDataList);
        } catch (Exception e) {
            log.error("Get batch error data failed for errorDataBatchNo: {}", errorDataBatchNo, e);
            return Result.fail("获取错误数据失败");
        }
    }

    /**
     * 清理批量操作错误数据
     */
    @DeleteMapping("/clearBatchErrorData/{errorDataBatchNo}")
    @ApiOperation(value = "清理批量操作错误数据", notes = "清理Redis中存储的错误数据，释放缓存空间")
    public Result<String> clearBatchErrorData(@PathVariable @ApiParam("错误数据批次号") String errorDataBatchNo) {
        try {
            batchValueAddedDeliveryOrderService.clearBatchErrorData(errorDataBatchNo);
            return Result.ok("错误数据清理成功");
        } catch (Exception e) {
            log.error("Clear batch error data failed for errorDataBatchNo: {}", errorDataBatchNo, e);
            return Result.fail("清理错误数据失败");
        }
    }

    /**
     * 导出批量操作错误数据
     */
    @GetMapping("/exportBatchErrorData/{errorDataBatchNo}")
    @ApiOperation(value = "导出批量操作错误数据", notes = "根据错误数据批次号导出失败记录的Excel文件")
    public void exportBatchErrorData(@PathVariable @ApiParam("错误数据批次号") String errorDataBatchNo,
                                   HttpServletResponse response) {
        try {
            log.info("Export batch error data request for errorDataBatchNo: {}", errorDataBatchNo);
            batchExportService.exportBatchErrorData(errorDataBatchNo, response);
        } catch (IOException e) {
            log.error("Export batch error data failed for errorDataBatchNo: {}", errorDataBatchNo, e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        }
    }

    /**
     * 导出批量操作详细记录
     */
    @GetMapping("/exportBatchOperationDetails/{batchNo}")
    @ApiOperation(value = "导出批量操作详细记录", notes = "根据批次号导出批量操作的详细记录Excel文件")
    public void exportBatchOperationDetails(@PathVariable @ApiParam("批次号") String batchNo,
                                          HttpServletResponse response) {
        try {
            log.info("Export batch operation details request for batchNo: {}", batchNo);
            batchExportService.exportBatchOperationDetails(batchNo, response);
        } catch (IOException e) {
            log.error("Export batch operation details failed for batchNo: {}", batchNo, e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        }
    }

    /**
     * 通过唯一码导出错误数据
     */
    @GetMapping("/exportByUniqueCode/{uniqueCode}")
    @ApiOperation(value = "通过唯一码导出错误数据", notes = "使用唯一导出码导出错误数据Excel文件")
    public void exportByUniqueCode(@PathVariable @ApiParam("唯一导出码") String uniqueCode,
                                 HttpServletResponse response) {
        try {
            log.info("Export by unique code request for uniqueCode: {}", uniqueCode);
            batchExportService.exportByUniqueCode(uniqueCode, response);
        } catch (IOException e) {
            log.error("Export by unique code failed for uniqueCode: {}", uniqueCode, e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        }
    }
}
