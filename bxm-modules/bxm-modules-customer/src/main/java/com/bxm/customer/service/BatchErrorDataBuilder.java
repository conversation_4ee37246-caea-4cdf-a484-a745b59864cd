package com.bxm.customer.service;

import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.valueadded.BatchErrorDataDTO;
import com.bxm.customer.domain.enums.BatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量错误数据构建器
 *
 * 扩展buildErrorDataList模式，专门处理批量操作中的错误数据
 * 负责构建错误数据并存储到Redis中
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Component
public class BatchErrorDataBuilder {

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationTypeMapper operationTypeMapper;

    /**
     * 构建批量操作错误数据列表并存储到Redis
     *
     * @param failedOrders 失败的交付单列表
     * @param errorMsgMap 错误信息映射（交付单编号 -> 错误信息）
     * @param batchNo 批次号
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     */
    public void buildBatchErrorDataList(List<ValueAddedDeliveryOrder> failedOrders,
                                       Map<String, String> errorMsgMap,
                                       String batchNo,
                                       String operationType,
                                       Long operatorId,
                                       String operatorName) {
        if (failedOrders == null || failedOrders.isEmpty()) {
            log.info("No failed orders to build error data for batch: {}", batchNo);
            return;
        }

        log.info("Building error data list for batch: {}, failed count: {}", batchNo, failedOrders.size());

        try {
            // 转换为错误数据DTO列表
            List<BatchErrorDataDTO> errorDataList = failedOrders.stream()
                    .map(order -> buildSingleErrorData(order, errorMsgMap, batchNo, operationType, operatorId, operatorName))
                    .collect(Collectors.toList());

            // 存储到Redis，使用分批存储机制
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATE_ERROR_RECORD + batchNo;
            redisService.setLargeCacheList(cacheKey, errorDataList, 500, 60 * 60, TimeUnit.SECONDS);

            log.info("Successfully stored {} error records to Redis with key: {}", errorDataList.size(), cacheKey);

        } catch (Exception e) {
            log.error("Failed to build error data list for batch: {}", batchNo, e);
            throw new RuntimeException("构建错误数据失败", e);
        }
    }

    /**
     * 构建单个错误数据记录
     *
     * @param order 失败的交付单
     * @param errorMsgMap 错误信息映射
     * @param batchNo 批次号
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 错误数据DTO
     */
    private BatchErrorDataDTO buildSingleErrorData(ValueAddedDeliveryOrder order,
                                                  Map<String, String> errorMsgMap,
                                                  String batchNo,
                                                  String operationType,
                                                  Long operatorId,
                                                  String operatorName) {
        // 获取当前状态信息
        ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
        String currentStatusDesc = currentStatus != null ? currentStatus.getDescription() : order.getStatus();

        // 获取目标状态信息
        ValueAddedDeliveryOrderStatus targetStatus = operationTypeMapper.getTargetStatus(operationType);
        String targetStatusDesc = targetStatus != null ? targetStatus.getDescription() : "";

        // 获取操作类型描述
        String operationTypeDesc = operationTypeMapper.getOperationDescription(operationType);

        // 获取错误信息
        String errorMsg = errorMsgMap.getOrDefault(order.getDeliveryOrderNo(), "未知错误");

        // 分析错误类型
        String errorType = analyzeErrorType(errorMsg);

        return BatchErrorDataDTO.builder()
                .deliveryOrderNo(order.getDeliveryOrderNo())
                .creditCode(order.getCreditCode() != null ? order.getCreditCode() : "")
                .topDeptId(order.getBusinessTopDeptId())
                .topDeptName("") // 这个字段需要在调用方填充部门名称
                .customerName(order.getCustomerName() != null ? order.getCustomerName() : "")
                .title(order.getTitle() != null ? order.getTitle() : "")
                .currentStatus(order.getStatus() != null ? order.getStatus() : "")
                .currentStatusDesc(currentStatusDesc)
                .targetStatus(targetStatus != null ? targetStatus.getCode() : "")
                .targetStatusDesc(targetStatusDesc)
                .operationType(operationType)
                .operationTypeDesc(operationTypeDesc)
                .errorMsg(errorMsg)
                .errorType(errorType)
                .batchNo(batchNo)
                .operatorId(operatorId)
                .operatorName(operatorName)
                .build();
    }

    /**
     * 分析错误类型
     *
     * @param errorMsg 错误信息
     * @return 错误类型
     */
    private String analyzeErrorType(String errorMsg) {
        if (StringUtils.isEmpty(errorMsg)) {
            return "未知错误";
        }

        String lowerErrorMsg = errorMsg.toLowerCase();
        
        if (lowerErrorMsg.contains("状态") && lowerErrorMsg.contains("转换")) {
            return "状态转换错误";
        } else if (lowerErrorMsg.contains("权限")) {
            return "权限验证错误";
        } else if (lowerErrorMsg.contains("数据") && lowerErrorMsg.contains("验证")) {
            return "数据验证错误";
        } else if (lowerErrorMsg.contains("不存在")) {
            return "数据不存在错误";
        } else if (lowerErrorMsg.contains("系统")) {
            return "系统错误";
        } else {
            return "业务逻辑错误";
        }
    }

    /**
     * 从Redis获取批量操作错误数据
     *
     * @param batchNo 批次号
     * @return 错误数据列表
     */
    public List<BatchErrorDataDTO> getBatchErrorDataList(String batchNo) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATE_ERROR_RECORD + batchNo;
            List<BatchErrorDataDTO> errorDataList = redisService.getLargeCacheList(cacheKey, 500);
            
            log.info("Retrieved {} error records from Redis for batch: {}", 
                    errorDataList != null ? errorDataList.size() : 0, batchNo);
            
            return errorDataList;
        } catch (Exception e) {
            log.error("Failed to get error data list for batch: {}", batchNo, e);
            throw new RuntimeException("获取错误数据失败", e);
        }
    }

    /**
     * 清理批量操作错误数据
     *
     * @param batchNo 批次号
     */
    public void clearBatchErrorData(String batchNo) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATE_ERROR_RECORD + batchNo;
            redisService.deleteLargeCacheList(cacheKey);
            log.info("Cleared error data for batch: {}", batchNo);
        } catch (Exception e) {
            log.error("Failed to clear error data for batch: {}", batchNo, e);
        }
    }
}
