package com.bxm.customer.domain.dto.batch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量状态变更请求DTO
 *
 * 用于增值交付单批量状态变更的请求参数封装
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量状态变更请求DTO")
public class BatchStatusChangeRequestDTO {

    /**
     * 交付单编号列表
     */
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次批量操作不能超过1000条记录")
    @ApiModelProperty(value = "交付单编号列表", required = true)
    private List<String> deliveryOrderNos;

    /**
     * 批量操作类型
     */
    @NotBlank(message = "批量操作类型不能为空")
    @ApiModelProperty(value = "批量操作类型", required = true, example = "BATCH_CONFIRM")
    private String batchOperationType;

    /**
     * 变更原因
     */
    @Size(max = 500, message = "变更原因长度不能超过500个字符")
    @ApiModelProperty(value = "变更原因", example = "批量确认交付单")
    private String reason;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1001")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @Size(max = 100, message = "操作人姓名长度不能超过100个字符")
    @ApiModelProperty(value = "操作人姓名", example = "张三")
    private String operatorName;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @ApiModelProperty(value = "备注信息", example = "批量操作备注")
    private String remark;

    /**
     * 顶级业务部门id
     */
    @ApiModelProperty(value = "集团id")
    private Long businessTopDeptId;
}
