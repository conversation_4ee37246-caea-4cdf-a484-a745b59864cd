package com.bxm.customer.domain.dto.valueadded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 批量操作进度DTO
 *
 * 用于跟踪批量操作的执行进度
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作进度DTO")
public class BatchOperationProgress {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", example = "BATCH_20250817_001")
    private String batchNo;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型", example = "BATCH_CONFIRM")
    private String operationType;

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述", example = "批量确认")
    private String operationTypeDesc;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100")
    private Integer totalCount;

    /**
     * 已处理记录数
     */
    @ApiModelProperty(value = "已处理记录数", example = "50")
    private Integer processedCount;

    /**
     * 成功记录数
     */
    @ApiModelProperty(value = "成功记录数", example = "45")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @ApiModelProperty(value = "失败记录数", example = "5")
    private Integer failCount;

    /**
     * 进度百分比
     */
    @ApiModelProperty(value = "进度百分比", example = "50.0")
    private Double progressPercentage;

    /**
     * 操作状态
     */
    @ApiModelProperty(value = "操作状态", example = "PROCESSING")
    private String status;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述", example = "处理中")
    private String statusDesc;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 当前时间
     */
    @ApiModelProperty(value = "当前时间")
    private LocalDateTime currentTime;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    private LocalDateTime estimatedEndTime;

    /**
     * 当前处理的交付单编号
     */
    @ApiModelProperty(value = "当前处理的交付单编号", example = "VAD2508051430001A1C")
    private String currentProcessingOrderNo;

    /**
     * 错误信息（如果有）
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID", example = "1001")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名", example = "张三")
    private String operatorName;

    /**
     * 批量操作状态枚举
     */
    public enum Status {
        PENDING("PENDING", "等待中"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
