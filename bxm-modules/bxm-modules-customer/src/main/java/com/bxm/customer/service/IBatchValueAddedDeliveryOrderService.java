package com.bxm.customer.service;

import com.bxm.customer.domain.dto.valueadded.BatchOperationResult;
import com.bxm.customer.domain.dto.valueadded.BatchStatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueadded.BatchErrorDataDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationProgress;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量增值交付单服务接口
 *
 * 提供增值交付单的批量操作功能
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
public interface IBatchValueAddedDeliveryOrderService {

    /**
     * 批量修改增值交付单状态
     *
     * 通过装饰器模式包装现有的单个状态转换逻辑，实现批量操作
     * 支持部分成功/失败的情况，提供详细的操作结果
     *
     * @param request 批量状态变更请求
     * @return 批量操作结果，包含成功/失败统计和错误详情
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当系统异常时抛出
     */
    BatchOperationResult batchChangeStatus(@Valid @NotNull BatchStatusChangeRequestDTO request);

    /**
     * 获取批量操作的错误数据列表
     *
     * 用于查询批量操作中失败记录的详细信息
     *
     * @param errorDataBatchNo 错误数据批次号
     * @return 错误数据列表
     */
    List<BatchErrorDataDTO> getBatchErrorDataList(String errorDataBatchNo);

    /**
     * 清理批量操作的错误数据
     *
     * 清理Redis中存储的错误数据，释放缓存空间
     *
     * @param errorDataBatchNo 错误数据批次号
     */
    void clearBatchErrorData(String errorDataBatchNo);

    /**
     * 获取批量操作结果
     *
     * 根据批次号查询批量操作的结果信息
     *
     * @param batchNo 批次号
     * @return 批量操作结果，如果不存在则返回null
     */
    BatchOperationResult getBatchOperationResult(String batchNo);

    /**
     * 验证批量操作请求
     *
     * 在执行批量操作前进行预验证，检查操作类型、状态转换等
     *
     * @param request 批量状态变更请求
     * @return 验证结果，包含可操作的交付单列表和不可操作的原因
     */
    BatchOperationResult validateBatchRequest(@Valid @NotNull BatchStatusChangeRequestDTO request);

    /**
     * 获取批量操作进度
     *
     * 查询批量操作的实时进度信息
     *
     * @param batchNo 批次号
     * @return 批量操作进度，如果不存在则返回null
     */
    BatchOperationProgress getBatchOperationProgress(String batchNo);

    /**
     * 更新批量操作进度
     *
     * 内部方法，用于更新批量操作的进度信息
     *
     * @param progress 进度信息
     */
    void updateBatchOperationProgress(BatchOperationProgress progress);
}
