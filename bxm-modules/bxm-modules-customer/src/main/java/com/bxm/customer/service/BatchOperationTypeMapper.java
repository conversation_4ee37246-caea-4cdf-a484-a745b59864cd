package com.bxm.customer.service;

import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueadded.BatchStatusChangeRequestDTO;
import com.bxm.customer.domain.enums.BatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 批量操作类型映射器
 *
 * 负责将批量操作请求转换为单个状态变更请求
 * 提供批量操作类型与状态转换的映射逻辑
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Component
public class BatchOperationTypeMapper {

    /**
     * 将批量状态变更请求转换为单个状态变更请求列表
     *
     * @param batchRequest 批量状态变更请求
     * @return 单个状态变更请求列表
     * @throws IllegalArgumentException 当操作类型不支持时抛出
     */
    public List<StatusChangeRequestDTO> mapToSingleRequests(BatchStatusChangeRequestDTO batchRequest) {
        log.info("Mapping batch request to single requests, operation type: {}, count: {}", 
                batchRequest.getBatchOperationType(), batchRequest.getDeliveryOrderNos().size());

        // 获取批量操作类型
        BatchOperationType operationType = BatchOperationType.getByCode(batchRequest.getBatchOperationType());
        if (operationType == null) {
            throw new IllegalArgumentException("不支持的批量操作类型: " + batchRequest.getBatchOperationType());
        }

        // 转换为单个状态变更请求列表
        return batchRequest.getDeliveryOrderNos().stream()
                .map(deliveryOrderNo -> createSingleRequest(deliveryOrderNo, operationType, batchRequest))
                .collect(Collectors.toList());
    }

    /**
     * 创建单个状态变更请求
     *
     * @param deliveryOrderNo 交付单编号
     * @param operationType 批量操作类型
     * @param batchRequest 批量请求
     * @return 单个状态变更请求
     */
    private StatusChangeRequestDTO createSingleRequest(String deliveryOrderNo, 
                                                     BatchOperationType operationType, 
                                                     BatchStatusChangeRequestDTO batchRequest) {
        return StatusChangeRequestDTO.builder()
                .deliveryOrderNo(deliveryOrderNo)
                .targetStatus(operationType.getToStatus().getCode())
                .reason(buildReason(operationType, batchRequest.getReason()))
                .operatorId(batchRequest.getOperatorId())
                .operatorName(batchRequest.getOperatorName())
                .remark(batchRequest.getRemark())
                .businessTopDeptId(batchRequest.getBusinessTopDeptId())
                .creditCode("") // 这个字段在批量操作中会在服务层填充
                .build();
    }

    /**
     * 构建变更原因
     *
     * @param operationType 操作类型
     * @param customReason 自定义原因
     * @return 完整的变更原因
     */
    private String buildReason(BatchOperationType operationType, String customReason) {
        String baseReason = operationType.getDescription();
        if (customReason != null && !customReason.trim().isEmpty()) {
            return baseReason + " - " + customReason.trim();
        }
        return baseReason;
    }

    /**
     * 验证批量操作类型是否支持指定的状态转换
     *
     * @param operationType 批量操作类型
     * @param currentStatus 当前状态
     * @return 是否支持
     */
    public boolean validateStatusTransition(BatchOperationType operationType, 
                                          ValueAddedDeliveryOrderStatus currentStatus) {
        if (operationType == null || currentStatus == null) {
            return false;
        }
        
        boolean isSupported = operationType.getFromStatus() == currentStatus;
        
        if (!isSupported) {
            log.warn("Status transition not supported: operation={}, expectedFrom={}, actualFrom={}", 
                    operationType.getCode(), 
                    operationType.getFromStatus().getDescription(), 
                    currentStatus.getDescription());
        }
        
        return isSupported;
    }

    /**
     * 获取操作类型的目标状态
     *
     * @param operationTypeCode 操作类型代码
     * @return 目标状态，如果操作类型不存在则返回null
     */
    public ValueAddedDeliveryOrderStatus getTargetStatus(String operationTypeCode) {
        BatchOperationType operationType = BatchOperationType.getByCode(operationTypeCode);
        return operationType != null ? operationType.getToStatus() : null;
    }

    /**
     * 获取操作类型的前置状态
     *
     * @param operationTypeCode 操作类型代码
     * @return 前置状态，如果操作类型不存在则返回null
     */
    public ValueAddedDeliveryOrderStatus getFromStatus(String operationTypeCode) {
        BatchOperationType operationType = BatchOperationType.getByCode(operationTypeCode);
        return operationType != null ? operationType.getFromStatus() : null;
    }

    /**
     * 获取操作类型描述
     *
     * @param operationTypeCode 操作类型代码
     * @return 操作类型描述，如果操作类型不存在则返回代码本身
     */
    public String getOperationDescription(String operationTypeCode) {
        BatchOperationType operationType = BatchOperationType.getByCode(operationTypeCode);
        return operationType != null ? operationType.getDescription() : operationTypeCode;
    }
}
