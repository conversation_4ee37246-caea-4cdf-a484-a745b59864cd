package com.bxm.customer.domain.dto.valueadded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量操作错误数据DTO
 *
 * 用于存储和导出批量操作中的错误记录
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作错误数据DTO")
public class BatchErrorDataDTO {

    /**
     * 交付单编号
     */
    @ApiModelProperty(value = "交付单编号")
    @Excel(name = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @Excel(name = "统一社会信用代码")
    private String creditCode;

    /**
     * 集团ID（顶级业务部门ID）
     */
    @ApiModelProperty(value = "集团ID")
    @Excel(name = "集团ID")
    private Long topDeptId;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    @Excel(name = "集团名称")
    private String topDeptName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @Excel(name = "客户名称")
    private String customerName;

    /**
     * 交付单标题
     */
    @ApiModelProperty(value = "交付单标题")
    @Excel(name = "交付单标题")
    private String title;

    /**
     * 当前状态
     */
    @ApiModelProperty(value = "当前状态")
    @Excel(name = "当前状态")
    private String currentStatus;

    /**
     * 当前状态描述
     */
    @ApiModelProperty(value = "当前状态描述")
    @Excel(name = "当前状态描述")
    private String currentStatusDesc;

    /**
     * 目标状态
     */
    @ApiModelProperty(value = "目标状态")
    @Excel(name = "目标状态")
    private String targetStatus;

    /**
     * 目标状态描述
     */
    @ApiModelProperty(value = "目标状态描述")
    @Excel(name = "目标状态描述")
    private String targetStatusDesc;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    @Excel(name = "操作类型")
    private String operationType;

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述")
    @Excel(name = "操作类型描述")
    private String operationTypeDesc;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    @Excel(name = "错误信息")
    private String errorMsg;

    /**
     * 错误类型
     */
    @ApiModelProperty(value = "错误类型")
    @Excel(name = "错误类型")
    private String errorType;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @Excel(name = "批次号")
    private String batchNo;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    @Excel(name = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @Excel(name = "操作人姓名")
    private String operatorName;
}
