package com.bxm.customer.domain.enums;

import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 批量操作类型枚举
 * 
 * 定义各种批量操作类型及其对应的状态转换规则
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Getter
@AllArgsConstructor
public enum BatchOperationType {

    /**
     * 批量确认 - 提交人确认交付单
     * 从"已提交待交付"状态转换到"已交付待确认"状态
     */
    BATCH_CONFIRM("BATCH_CONFIRM", "批量确认", 
                  ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, 
                  ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION),

    /**
     * 批量提交 - 提交人准备批量提交交付单
     * 从"已保存待提交"状态转换到"已提交待交付"状态
     */
    BATCH_SUBMIT("BATCH_SUBMIT", "批量提交", 
                 ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT, 
                 ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY),

    /**
     * 批量关闭扣款 - 提交人关闭扣款
     * 从"已确认待扣款"状态转换到"已关闭扣款"状态
     */
    BATCH_CLOSE_DEDUCTION("BATCH_CLOSE_DEDUCTION", "批量关闭扣款", 
                          ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, 
                          ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED),

    /**
     * 批量关闭交付 - 提交人关闭交付
     * 从"已提交待交付"状态转换到"已关闭交付"状态
     */
    BATCH_CLOSE_DELIVERY("BATCH_CLOSE_DELIVERY", "批量关闭交付", 
                         ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, 
                         ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED),

    /**
     * 批量解除扣款异常 - 提交人批量解除扣款异常
     * 从"扣款异常"状态转换到"已确认待扣款"状态
     */
    BATCH_RESOLVE_DEDUCTION_EXCEPTION("BATCH_RESOLVE_DEDUCTION_EXCEPTION", "批量解除扣款异常", 
                                      ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION, 
                                      ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION),

    /**
     * 批量解除交付异常 - 提交人批量解除交付异常
     * 从"交付异常"状态转换到"已交付待确认"状态
     */
    BATCH_RESOLVE_DELIVERY_EXCEPTION("BATCH_RESOLVE_DELIVERY_EXCEPTION", "批量解除交付异常", 
                                     ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION, 
                                     ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION),

    /**
     * 批量驳回 - 前置状态为已关闭交付的状态进行操作
     * 从"已关闭交付"状态转换到"已提交待交付"状态
     */
    BATCH_REJECT_FROM_CLOSED_DELIVERY("BATCH_REJECT_FROM_CLOSED_DELIVERY", "批量驳回", 
                                      ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED, 
                                      ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY),

    /**
     * 批量退回 - 前置状态为已关闭扣款状态进行操作
     * 从"已关闭扣款"状态转换到"已确认待扣款"状态
     */
    BATCH_RETURN_FROM_CLOSED_DEDUCTION("BATCH_RETURN_FROM_CLOSED_DEDUCTION", "批量退回", 
                                       ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED, 
                                       ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION);

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 前置状态（当前状态）
     */
    private final ValueAddedDeliveryOrderStatus fromStatus;

    /**
     * 目标状态
     */
    private final ValueAddedDeliveryOrderStatus toStatus;

    /**
     * 根据操作类型代码获取枚举
     *
     * @param code 操作类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BatchOperationType getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (BatchOperationType type : values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证操作类型代码是否有效
     *
     * @param code 操作类型代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 检查指定的状态转换是否被此操作类型支持
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否支持
     */
    public boolean supportsTransition(ValueAddedDeliveryOrderStatus currentStatus, 
                                    ValueAddedDeliveryOrderStatus targetStatus) {
        return this.fromStatus == currentStatus && this.toStatus == targetStatus;
    }
}
