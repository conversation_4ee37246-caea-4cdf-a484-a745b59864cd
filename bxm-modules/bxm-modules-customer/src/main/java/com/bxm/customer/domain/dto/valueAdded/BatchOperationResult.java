package com.bxm.customer.domain.dto.valueadded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量操作结果DTO
 *
 * 统一的批量操作返回结果格式
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作结果DTO")
public class BatchOperationResult {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", example = "BATCH_20250817_001")
    private String batchNo;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型", example = "BATCH_CONFIRM")
    private String operationType;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100")
    private Integer totalCount;

    /**
     * 成功记录数
     */
    @ApiModelProperty(value = "成功记录数", example = "95")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @ApiModelProperty(value = "失败记录数", example = "5")
    private Integer failCount;

    /**
     * 操作开始时间
     */
    @ApiModelProperty(value = "操作开始时间")
    private LocalDateTime startTime;

    /**
     * 操作结束时间
     */
    @ApiModelProperty(value = "操作结束时间")
    private LocalDateTime endTime;

    /**
     * 操作耗时（毫秒）
     */
    @ApiModelProperty(value = "操作耗时（毫秒）", example = "1500")
    private Long duration;

    /**
     * 错误数据批次号（用于导出错误数据）
     */
    @ApiModelProperty(value = "错误数据批次号", example = "ERROR_20250817_001")
    private String errorDataBatchNo;

    /**
     * 成功的交付单编号列表
     */
    @ApiModelProperty(value = "成功的交付单编号列表")
    private List<String> successDeliveryOrderNos;

    /**
     * 失败的交付单编号列表
     */
    @ApiModelProperty(value = "失败的交付单编号列表")
    private List<String> failDeliveryOrderNos;

    /**
     * 操作摘要信息
     */
    @ApiModelProperty(value = "操作摘要信息", example = "批量确认操作完成，成功95条，失败5条")
    private String summary;

    /**
     * 是否有错误数据
     */
    @ApiModelProperty(value = "是否有错误数据", example = "true")
    private Boolean hasErrors;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID", example = "1001")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名", example = "张三")
    private String operatorName;
}
