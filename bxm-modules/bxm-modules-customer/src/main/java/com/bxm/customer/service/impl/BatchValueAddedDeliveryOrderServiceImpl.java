package com.bxm.customer.service.impl;

import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueadded.BatchErrorDataDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationResult;
import com.bxm.customer.domain.dto.valueadded.BatchStatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueadded.BatchOperationProgress;
import com.bxm.customer.domain.enums.BatchOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.BatchErrorDataBuilder;
import com.bxm.customer.service.BatchOperationTypeMapper;
import com.bxm.customer.service.IBatchValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量增值交付单服务实现类
 *
 * 使用装饰器模式包装现有的单个状态转换逻辑，实现批量操作
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
public class BatchValueAddedDeliveryOrderServiceImpl implements IBatchValueAddedDeliveryOrderService {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private BatchOperationTypeMapper operationTypeMapper;

    @Autowired
    private BatchErrorDataBuilder errorDataBuilder;

    @Autowired
    private RedisService redisService;

    @Override
    public BatchOperationResult batchChangeStatus(BatchStatusChangeRequestDTO request) {
        log.info("Starting batch status change operation, type: {}, count: {}", 
                request.getBatchOperationType(), request.getDeliveryOrderNos().size());

        LocalDateTime startTime = LocalDateTime.now();
        String batchNo = generateBatchNo();
        
        try {
            // 1. 验证批量操作类型
            BatchOperationType operationType = BatchOperationType.getByCode(request.getBatchOperationType());
            if (operationType == null) {
                throw new IllegalArgumentException("不支持的批量操作类型: " + request.getBatchOperationType());
            }

            // 2. 初始化进度跟踪
            initializeBatchProgress(batchNo, request);

            // 3. 转换为单个状态变更请求列表
            List<StatusChangeRequestDTO> singleRequests = operationTypeMapper.mapToSingleRequests(request);

            // 4. 执行批量操作
            BatchOperationResult result = executeBatchOperation(singleRequests, batchNo, request);

            // 4. 设置操作信息
            result.setBatchNo(batchNo);
            result.setOperationType(request.getBatchOperationType());
            result.setStartTime(startTime);
            result.setEndTime(LocalDateTime.now());
            result.setDuration(System.currentTimeMillis() - toMillis(startTime));
            result.setOperatorId(request.getOperatorId());
            result.setOperatorName(request.getOperatorName());

            // 5. 缓存操作结果
            cacheOperationResult(result);

            log.info("Batch operation completed, batchNo: {}, total: {}, success: {}, fail: {}", 
                    batchNo, result.getTotalCount(), result.getSuccessCount(), result.getFailCount());

            return result;

        } catch (Exception e) {
            log.error("Batch operation failed, batchNo: {}", batchNo, e);
            
            // 创建失败结果
            BatchOperationResult failResult = BatchOperationResult.builder()
                    .batchNo(batchNo)
                    .operationType(request.getBatchOperationType())
                    .totalCount(request.getDeliveryOrderNos().size())
                    .successCount(0)
                    .failCount(request.getDeliveryOrderNos().size())
                    .startTime(startTime)
                    .endTime(LocalDateTime.now())
                    .duration(System.currentTimeMillis() - toMillis(startTime))
                    .hasErrors(true)
                    .summary("批量操作失败: " + e.getMessage())
                    .operatorId(request.getOperatorId())
                    .operatorName(request.getOperatorName())
                    .build();

            cacheOperationResult(failResult);
            throw e;
        }
    }

    /**
     * 执行批量操作
     */
    private BatchOperationResult executeBatchOperation(List<StatusChangeRequestDTO> singleRequests, 
                                                     String batchNo, 
                                                     BatchStatusChangeRequestDTO batchRequest) {
        List<String> successOrderNos = new ArrayList<>();
        List<String> failOrderNos = new ArrayList<>();
        List<ValueAddedDeliveryOrder> failedOrders = new ArrayList<>();
        Map<String, String> errorMsgMap = new HashMap<>();

        // 逐个执行状态变更
        for (StatusChangeRequestDTO singleRequest : singleRequests) {
            try {
                // 调用现有的单个状态变更方法（装饰器模式的核心）
                valueAddedDeliveryOrderService.changeStatus(singleRequest);
                successOrderNos.add(singleRequest.getDeliveryOrderNo());
                
                log.debug("Successfully changed status for order: {}", singleRequest.getDeliveryOrderNo());
                
            } catch (Exception e) {
                failOrderNos.add(singleRequest.getDeliveryOrderNo());
                errorMsgMap.put(singleRequest.getDeliveryOrderNo(), e.getMessage());
                
                // 获取失败的订单信息（用于构建错误数据）
                try {
                    ValueAddedDeliveryOrder failedOrder = valueAddedDeliveryOrderService.getByDeliveryOrderNo(singleRequest.getDeliveryOrderNo());
                    if (failedOrder != null) {
                        // 填充缺失的字段
                        if (failedOrder.getCreditCode() == null || failedOrder.getCreditCode().isEmpty()) {
                            failedOrder.setCreditCode(singleRequest.getCreditCode());
                        }
                        failedOrders.add(failedOrder);
                    }
                } catch (Exception ex) {
                    log.warn("Failed to get order info for error data: {}", singleRequest.getDeliveryOrderNo(), ex);
                    // 创建一个基本的订单对象用于错误记录
                    ValueAddedDeliveryOrder basicOrder = new ValueAddedDeliveryOrder();
                    basicOrder.setDeliveryOrderNo(singleRequest.getDeliveryOrderNo());
                    basicOrder.setCreditCode(singleRequest.getCreditCode());
                    basicOrder.setBusinessTopDeptId(singleRequest.getBusinessTopDeptId());
                    basicOrder.setStatus("UNKNOWN");
                    basicOrder.setTitle("获取订单信息失败");
                    basicOrder.setCustomerName("未知客户");
                    failedOrders.add(basicOrder);
                }
                
                log.warn("Failed to change status for order: {}, error: {}", 
                        singleRequest.getDeliveryOrderNo(), e.getMessage());
            }
        }

        // 构建错误数据
        String errorDataBatchNo = null;
        if (!failedOrders.isEmpty()) {
            errorDataBatchNo = generateErrorDataBatchNo();
            errorDataBuilder.buildBatchErrorDataList(
                    failedOrders, 
                    errorMsgMap, 
                    errorDataBatchNo, 
                    batchRequest.getBatchOperationType(),
                    batchRequest.getOperatorId(),
                    batchRequest.getOperatorName()
            );
        }

        // 构建操作摘要
        String summary = String.format("批量%s操作完成，总计%d条，成功%d条，失败%d条", 
                operationTypeMapper.getOperationDescription(batchRequest.getBatchOperationType()),
                singleRequests.size(), successOrderNos.size(), failOrderNos.size());

        return BatchOperationResult.builder()
                .totalCount(singleRequests.size())
                .successCount(successOrderNos.size())
                .failCount(failOrderNos.size())
                .successDeliveryOrderNos(successOrderNos)
                .failDeliveryOrderNos(failOrderNos)
                .errorDataBatchNo(errorDataBatchNo)
                .hasErrors(!failOrderNos.isEmpty())
                .summary(summary)
                .build();
    }

    @Override
    public List<BatchErrorDataDTO> getBatchErrorDataList(String errorDataBatchNo) {
        return errorDataBuilder.getBatchErrorDataList(errorDataBatchNo);
    }

    @Override
    public void clearBatchErrorData(String errorDataBatchNo) {
        errorDataBuilder.clearBatchErrorData(errorDataBatchNo);
    }

    @Override
    public BatchOperationResult getBatchOperationResult(String batchNo) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATION_RESULT + batchNo;
            return redisService.getCacheObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to get batch operation result for batchNo: {}", batchNo, e);
            return null;
        }
    }

    @Override
    public BatchOperationResult validateBatchRequest(BatchStatusChangeRequestDTO request) {
        // TODO: 实现批量操作预验证逻辑
        // 这里可以预先检查每个交付单的状态是否符合操作要求
        // 暂时返回简单的验证结果
        return BatchOperationResult.builder()
                .totalCount(request.getDeliveryOrderNos().size())
                .successCount(request.getDeliveryOrderNos().size())
                .failCount(0)
                .hasErrors(false)
                .summary("预验证通过")
                .build();
    }

    /**
     * 缓存操作结果
     */
    private void cacheOperationResult(BatchOperationResult result) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATION_RESULT + result.getBatchNo();
            redisService.setCacheObject(cacheKey, result, 24 * 60 * 60L, TimeUnit.SECONDS); // 缓存24小时
        } catch (Exception e) {
            log.error("Failed to cache operation result for batchNo: {}", result.getBatchNo(), e);
        }
    }

    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" + StringUtils.getUuid().substring(0, 8).toUpperCase();
    }

    /**
     * 生成错误数据批次号
     */
    private String generateErrorDataBatchNo() {
        return "ERROR_" + System.currentTimeMillis() + "_" + StringUtils.getUuid().substring(0, 8).toUpperCase();
    }

    @Override
    public BatchOperationProgress getBatchOperationProgress(String batchNo) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATION_PROGRESS + batchNo;
            return redisService.getCacheObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to get batch operation progress for batchNo: {}", batchNo, e);
            return null;
        }
    }

    @Override
    public void updateBatchOperationProgress(BatchOperationProgress progress) {
        try {
            String cacheKey = CacheConstants.VALUE_ADDED_BATCH_OPERATION_PROGRESS + progress.getBatchNo();
            redisService.setCacheObject(cacheKey, progress, 24 * 60 * 60L, TimeUnit.SECONDS); // 缓存24小时
        } catch (Exception e) {
            log.error("Failed to update batch operation progress for batchNo: {}", progress.getBatchNo(), e);
        }
    }

    /**
     * 初始化批量操作进度
     */
    private void initializeBatchProgress(String batchNo, BatchStatusChangeRequestDTO request) {
        BatchOperationProgress progress = BatchOperationProgress.builder()
                .batchNo(batchNo)
                .operationType(request.getBatchOperationType())
                .operationTypeDesc(operationTypeMapper.getOperationDescription(request.getBatchOperationType()))
                .totalCount(request.getDeliveryOrderNos().size())
                .processedCount(0)
                .successCount(0)
                .failCount(0)
                .progressPercentage(0.0)
                .status(BatchOperationProgress.Status.PROCESSING.getCode())
                .statusDesc(BatchOperationProgress.Status.PROCESSING.getDescription())
                .startTime(LocalDateTime.now())
                .currentTime(LocalDateTime.now())
                .operatorId(request.getOperatorId())
                .operatorName(request.getOperatorName())
                .build();

        updateBatchOperationProgress(progress);
    }

    /**
     * 转换LocalDateTime为毫秒
     */
    private long toMillis(LocalDateTime dateTime) {
        return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
