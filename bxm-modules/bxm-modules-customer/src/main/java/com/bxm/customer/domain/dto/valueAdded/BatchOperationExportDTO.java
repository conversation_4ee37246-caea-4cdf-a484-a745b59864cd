package com.bxm.customer.domain.dto.valueadded;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 批量操作导出数据DTO
 *
 * 用于导出批量操作的详细记录
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量操作导出数据DTO")
public class BatchOperationExportDTO {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @Excel(name = "批次号")
    private String batchNo;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    @Excel(name = "操作类型")
    private String operationType;

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述")
    @Excel(name = "操作类型描述")
    private String operationTypeDesc;

    /**
     * 交付单编号
     */
    @ApiModelProperty(value = "交付单编号")
    @Excel(name = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @Excel(name = "统一社会信用代码")
    private String creditCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @Excel(name = "客户名称")
    private String customerName;

    /**
     * 交付单标题
     */
    @ApiModelProperty(value = "交付单标题")
    @Excel(name = "交付单标题")
    private String title;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    @Excel(name = "集团名称")
    private String topDeptName;

    /**
     * 原状态
     */
    @ApiModelProperty(value = "原状态")
    @Excel(name = "原状态")
    private String originalStatus;

    /**
     * 原状态描述
     */
    @ApiModelProperty(value = "原状态描述")
    @Excel(name = "原状态描述")
    private String originalStatusDesc;

    /**
     * 目标状态
     */
    @ApiModelProperty(value = "目标状态")
    @Excel(name = "目标状态")
    private String targetStatus;

    /**
     * 目标状态描述
     */
    @ApiModelProperty(value = "目标状态描述")
    @Excel(name = "目标状态描述")
    private String targetStatusDesc;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    @Excel(name = "处理结果", readConverterExp = "SUCCESS=成功,FAILED=失败")
    private String result;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    @Excel(name = "错误信息")
    private String errorMsg;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    @Excel(name = "操作人姓名")
    private String operatorName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @Excel(name = "变更原因")
    private String reason;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    @Excel(name = "备注信息")
    private String remark;
}
